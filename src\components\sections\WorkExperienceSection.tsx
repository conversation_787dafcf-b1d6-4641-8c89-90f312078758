"use client";

import { Briefcase, Calendar, MapPin, ExternalLink } from "lucide-react";

// Work experience data
const workExperience = [
  {
    id: 1,
    company: "Freelance Client",
    position: "Backend Developer",
    location: "Remote",
    startDate: "feb 2025",
    duration: "3 months",
    description: "Developed a comprehensive e-commerce backend system for a retail client, handling inventory management, order processing, and payment integration.",
    responsibilities: [
      "Designed and implemented RESTful APIs for product catalog and inventory management",
      "Built secure payment processing system with Stripe integration",
      "Developed real-time order tracking and notification system",
      "Implemented user authentication and authorization with JWT",
      "Optimized database queries for improved performance and scalability",
      "Created comprehensive API documentation and testing suites"
    ],
    technologies: ["Node.js", "Express.js", "PostgreSQL", "Redis", "Stripe API", "JWT", "Docker"],
    type: "Freelance Project",
    website: null
  },
  {
    id: 2,
    company: "Freelance Client",
    position: "Backend Developer",
    location: "Remote",
    startDate: "April 2025",
    duration: "3 months",
    description: "Built a scalable task management and collaboration platform backend using Node.js and TypeScript, serving over 1000+ concurrent users.",
    responsibilities: [
      "Architected microservices-based backend system with TypeScript",
      "Implemented real-time collaboration features using WebSocket connections",
      "Developed advanced user role management and permissions system",
      "Built automated email notification system for task updates",
      "Integrated third-party APIs for calendar synchronization",
      "Implemented comprehensive logging and monitoring solutions"
    ],
    technologies: ["Node.js", "TypeScript", "Express.js", "MongoDB", "Socket.io", "Redis", "AWS"],
    type: "Freelance Project",
    website: null
  },
  {
    id: 3,
    company: "Browzy AI",
    position: "Product Developer",
    location: "Remote",
    startDate: "june 2025",
    description: "Leading the development of Browzy AI, an intelligent browser assistant extension for Chrome and Brave browsers that enhances web browsing with AI-powered automation and productivity features.",
    responsibilities: [
      "Developed Chrome extension architecture using Manifest V3",
      "Implemented AI-powered web page analysis and content extraction",
      "Built intelligent form auto-filling and data management features",
      "Created seamless integration with Brave browser ecosystem",
      "Developed user-friendly dashboard for managing AI preferences",
      "Implemented privacy-focused data handling and local storage solutions"
    ],
    technologies: ["JavaScript", "TypeScript", "Chrome Extension API", "AI/ML APIs", "React", "Webpack"],
    type: "Product Development",
    website: "https://browzyai.com"
  },

];

export default function WorkExperienceSection() {
  return (
    <section id="work-experience" className="px-6 lg:px-12 mt-24">
      <div className="max-w-7xl mx-auto">
        <div className="mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-deep-charcoal dark:text-dark-text mb-4">
            Work Experience
          </h2>
          <p className="text-deep-charcoal/70 dark:text-dark-text/70 text-lg">
            My professional journey as a Full Stack Developer and Software Engineer
          </p>
        </div>

        {/* Desktop Layout */}
        <div className="hidden md:block space-y-8">
          {workExperience.map((experience, index) => (
            <div
              key={experience.id}
              className="group relative"
            >
              {/* Timeline line */}
              {index !== workExperience.length - 1 && (
                <div className="absolute left-6 top-16 w-0.5 h-full bg-deep-charcoal/20 dark:bg-dark-text/20"></div>
              )}

              {/* Experience Card */}
              <div className="flex gap-6">
                {/* Timeline dot */}
                <div className="flex-shrink-0 w-12 h-12 bg-accent-green rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Briefcase className="w-6 h-6 text-white" />
                </div>

                {/* Content */}
                <div className="flex-1 pb-8">
                  <div className="bg-deep-charcoal/5 dark:bg-dark-text/5 rounded-lg p-6 hover:bg-deep-charcoal/10 dark:hover:bg-dark-text/10 transition-all duration-300">
                    {/* Header */}
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                      <div>
                        <h3 className="text-xl font-bold text-deep-charcoal dark:text-dark-text mb-1">
                          {experience.position}
                        </h3>
                        <div className="flex items-center gap-2 text-accent-green font-medium mb-2">
                          <span>{experience.company}</span>
                          {experience.website && (
                            <a
                              href={experience.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="hover:text-accent-green/80 transition-colors"
                            >
                              <ExternalLink className="w-4 h-4" />
                            </a>
                          )}
                        </div>
                      </div>

                      <div className="flex flex-col sm:items-end gap-1">
                        <div className="flex items-center gap-1 text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                          <Calendar className="w-4 h-4" />
                          <span>{experience.startDate}</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                          <MapPin className="w-4 h-4" />
                          <span>{experience.location}</span>
                        </div>
                        <span className="text-xs bg-accent-green/10 text-accent-green px-2 py-1 rounded-full">
                          {experience.type}
                        </span>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-deep-charcoal/80 dark:text-dark-text/80 mb-4 leading-relaxed">
                      {experience.description}
                    </p>

                    {/* Responsibilities */}
                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-deep-charcoal dark:text-dark-text mb-2">
                        Key Responsibilities:
                      </h4>
                      <ul className="list-disc list-inside text-sm text-deep-charcoal/70 dark:text-dark-text/70 space-y-1">
                        {experience.responsibilities.map((responsibility, idx) => (
                          <li key={idx}>{responsibility}</li>
                        ))}
                      </ul>
                    </div>

                    {/* Technologies */}
                    <div>
                      <h4 className="text-sm font-semibold text-deep-charcoal dark:text-dark-text mb-2">
                        Technologies Used:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {experience.technologies.map((tech, idx) => (
                          <span
                            key={idx}
                            className="px-3 py-1 text-xs font-medium bg-deep-charcoal/10 dark:bg-dark-text/10 text-deep-charcoal/80 dark:text-dark-text/80 rounded-full"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Mobile Card Layout */}
        <div className="md:hidden space-y-3">
          {workExperience.map((experience) => (
            <div
              key={experience.id}
              className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200"
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-accent-green rounded-full flex items-center justify-center flex-shrink-0">
                    <Briefcase className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-deep-charcoal dark:text-dark-text leading-tight">
                      {experience.position}
                    </h3>
                    <div className="flex items-center gap-1 text-accent-green font-medium text-sm">
                      <span>{experience.company}</span>
                      {experience.website && (
                        <a
                          href={experience.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="hover:text-accent-green/80 transition-colors"
                        >
                          <ExternalLink className="w-3 h-3" />
                        </a>
                      )}
                    </div>
                  </div>
                </div>
                <span className="text-xs bg-accent-green/10 text-accent-green px-2 py-1 rounded-full flex-shrink-0">
                  {experience.type}
                </span>
              </div>

              {/* Timeline */}
              <div className="flex items-center gap-4 mb-3 text-xs text-deep-charcoal/70 dark:text-dark-text/70">
                <div className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  <span>{experience.startDate} - {experience.endDate}</span>
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="w-3 h-3" />
                  <span>{experience.location}</span>
                </div>
              </div>

              {/* Description - Shortened for mobile */}
              <p className="text-sm text-deep-charcoal/80 dark:text-dark-text/80 mb-3 leading-relaxed">
                {experience.description.length > 120
                  ? `${experience.description.substring(0, 120)}...`
                  : experience.description}
              </p>

              {/* Key Technologies - Limited to top 4 */}
              <div>
                <div className="flex flex-wrap gap-1">
                  {experience.technologies.slice(0, 4).map((tech, idx) => (
                    <span
                      key={idx}
                      className="px-2 py-1 text-xs font-medium bg-deep-charcoal/10 dark:bg-dark-text/10 text-deep-charcoal/80 dark:text-dark-text/80 rounded-full"
                    >
                      {tech}
                    </span>
                  ))}
                  {experience.technologies.length > 4 && (
                    <span className="px-2 py-1 text-xs font-medium bg-accent-green/10 text-accent-green rounded-full">
                      +{experience.technologies.length - 4} more
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="mt-12 text-center">
          <p className="text-deep-charcoal/70 dark:text-dark-text/70 mb-4">
            Interested in working together?
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <a
              href="#hire"
              className="inline-flex items-center px-6 py-3 bg-accent-green hover:bg-accent-green/80 text-white font-medium rounded-lg transition-colors"
            >
              Let's Talk
            </a>
            <div className="flex items-center gap-2 px-4 py-2 bg-accent-green/10 text-accent-green rounded-lg">
              <div className="w-2 h-2 bg-accent-green rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">Available Full Time</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
